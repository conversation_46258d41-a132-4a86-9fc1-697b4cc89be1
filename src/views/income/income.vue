<!-- @format -->

<template>
  <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" @handleScroll="pageScroll" company="rem" text="已加载完全部数据" :status="listStatus">
    <div class="main">
      <div class="user flex flex_ac" v-if="unionInfo.unionid > 0">
        <span class="head flex_s">
          <img v-lazy="unionInfo.headimg_url" :key="unionInfo.headimg_url" alt="" type="user" />
        </span>
        <div class="flex_1">
          <div class="flex name_box">
            <span class="name ws flex_s">{{ unionInfo.unionname }}</span>
            <em>(编号:{{ unionInfo.unionid }})</em>
          </div>
          <div class="grade">
            <span @click="$router.push('/level1')" v-if="Number(unionInfo.level1_flag) > 0 && unionInfo.level1 > 0" class="group" :class="'group' + unionInfo.level1">
              <i>
                <img :src="getPgroupUrl('pgroup', unionInfo.level1)" alt="" />
              </i>
              <em>{{ unionInfo.level1_name }}</em>
            </span>

            <span @click="$router.push('/level2')" v-if="Number(unionInfo.level2_flag) > 0 && unionInfo.level2 > 0" class="group" :class="'group' + unionInfo.level2">
              <i>
                <img :src="getPgroupUrl('tgroup', unionInfo.level2)" alt="" />
              </i>
              <em>{{ unionInfo.level2_name }}</em>
            </span>

            <span @click="$router.push('/level3')" v-if="Number(unionInfo.level3_flag) > 0 && unionInfo.level3 > 0" class="group" :class="'group' + unionInfo.level3">
              <i>
                <img :src="getPgroupUrl('sgroup', unionInfo.level3)" alt="" />
              </i>
              <em>{{ unionInfo.level3_name }}</em>
            </span>

            <span @click="$router.push('/level4')" v-if="Number(unionInfo.level4_flag) > 0 && unionInfo.level4 > 0" class="group" :class="'group' + unionInfo.level4">
              <i>
                <img :src="getPgroupUrl('cgroup', unionInfo.level4)" alt="" />
              </i>
              <em>{{ unionInfo.level4_name }}</em>
            </span>
          </div>
        </div>
      </div>
      <div v-else class="user_skelecton flex flex_ac">
        <span class="head flex_s"></span>
        <div class="flex_1">
          <p></p>
          <div class="flex flex_ac">
            <span v-for="v in 3"></span>
          </div>
        </div>
      </div>
      <template v-if="unionInfo.unionid > 0">
        <!--推广/线下-->
        <div v-if="(unionInfo.level1_flag == 1 || level4_flag == 1) && unionInfo.level2_flag != 1 && unionInfo.level3_flag != 1" class="data flex flex_ac flex_jsb">
          <div class="flex_dc flex_v">
            <span>{{ Number(income.month_income) >= 10000 ? (Number(income.month_income) / 10000).toFixed(2) : income.month_income }}</span>
            <p>本月收益({{ Number(income.month_income) >= 10000 ? '万' : '元' }})</p>
          </div>
          <div class="flex_dc flex_v">
            <span>{{ Number(income.remain_income) >= 10000 ? (Number(income.remain_income) / 10000).toFixed(2) : income.remain_income }}</span>
            <p>可提现({{ Number(income.remain_income) >= 10000 ? '万' : '元' }})</p>
          </div>
          <div class="flex_dc flex_v">
            <span>{{ Number(income.total_income) >= 10000 ? (Number(income.total_income) / 10000).toFixed(2) : income.total_income }}</span>
            <p>累计收益({{ Number(income.total_income) >= 10000 ? '万' : '元' }})</p>
          </div>
        </div>

        <!--推广/团长-->
        <template v-else-if="(unionInfo.level1_flag == 1 || level4_flag == 1) && unionInfo.level2_flag == 1 && unionInfo.level3_flag != 1">
          <div class="data flex flex_ac flex_jsb">
            <div class="flex_dc flex_v">
              <span>{{ Number(income.month_income) >= 10000 ? (Number(income.month_income) / 10000).toFixed(2) : income.month_income }}</span>
              <p>本月收益({{ Number(income.month_income) >= 10000 ? '万' : '元' }})</p>
            </div>
            <div class="flex_dc flex_v">
              <span>{{ Number(income.remain_income) >= 10000 ? (Number(income.remain_income) / 10000).toFixed(2) : income.remain_income }}</span>
              <p>可提现({{ Number(income.remain_income) >= 10000 ? '万' : '元' }})</p>
            </div>
          </div>
          <div class="data flex flex_ac flex_jsb">
            <div class="flex_dc flex_v">
              <span>{{ Number(income.leader_income) >= 10000 ? (Number(income.leader_income) / 10000).toFixed(2) : income.leader_income }}</span>
              <p>团队收益({{ Number(income.leader_income) >= 10000 ? '万' : '元' }})</p>
            </div>
            <div class="flex_dc flex_v">
              <span>{{ Number(income.total_income) >= 10000 ? (Number(income.total_income) / 10000).toFixed(2) : income.total_income }}</span>
              <p>个人累计收益({{ Number(income.total_income) >= 10000 ? '万' : '元' }})</p>
            </div>
          </div>
        </template>
        <!--推广/服务-->
        <template v-else-if="(unionInfo.level1_flag == 1 || level4_flag == 1) && unionInfo.level2_flag != 1 && unionInfo.level3_flag == 1">
          <div class="data flex flex_ac flex_jsb">
            <div class="flex_dc flex_v">
              <span>{{ Number(income.month_income) >= 10000 ? (Number(income.month_income) / 10000).toFixed(2) : income.month_income }}</span>
              <p>本月收益({{ Number(income.month_income) >= 10000 ? '万' : '元' }})</p>
            </div>
            <div class="flex_dc flex_v">
              <span>{{ Number(income.remain_income) >= 10000 ? (Number(income.remain_income) / 10000).toFixed(2) : income.remain_income }}</span>
              <p>可提现({{ Number(income.remain_income) >= 10000 ? '万' : '元' }})</p>
            </div>
            <div class="flex_dc flex_v">
              <span>{{ Number(income.total_income) >= 10000 ? (Number(income.total_income) / 10000).toFixed(2) : income.total_income }}</span>
              <p>累计收益({{ Number(income.total_income) >= 10000 ? '万' : '元' }})</p>
            </div>
          </div>
          <div class="data flex flex_ac flex_jsb">
            <div class="flex_dc flex_v">
              <span>{{ Number(income.audit_income) >= 10000 ? (Number(income.audit_income) / 10000).toFixed(2) : income.audit_income }}</span>
              <p>审核资料收益({{ Number(income.audit_income) >= 10000 ? '万' : '元' }})</p>
            </div>
            <div class="flex_dc flex_v">
              <span>{{ Number(income.after_income) >= 10000 ? (Number(income.after_income) / 10000).toFixed(2) : income.after_income }}</span>
              <p>帮约收益({{ Number(income.after_income) >= 10000 ? '万' : '元' }})</p>
            </div>
          </div>
        </template>
        <!--全部-->
        <template v-else-if="(unionInfo.level1_flag == 1 || unionInfo.level4_flag == 1) && unionInfo.level2_flag == 1 && unionInfo.level3_flag == 1">
          <div class="data flex flex_ac flex_jsb">
            <div class="flex_dc flex_v">
              <span>{{ Number(income.month_income) >= 10000 ? (Number(income.month_income) / 10000).toFixed(2) : income.month_income }}</span>
              <p>本月收益({{ Number(income.month_income) >= 10000 ? '万' : '元' }})</p>
            </div>
            <div class="flex_dc flex_v">
              <span>{{ Number(income.remain_income) >= 10000 ? (Number(income.remain_income) / 10000).toFixed(2) : income.remain_income }}</span>
              <p>可提现({{ Number(income.remain_income) >= 10000 ? '万' : '元' }})</p>
            </div>
            <div class="flex_dc flex_v">
              <span>{{ Number(income.total_income) >= 10000 ? (Number(income.total_income) / 10000).toFixed(2) : income.total_income }}</span>
              <p>累计收益({{ Number(income.total_income) >= 10000 ? '万' : '元' }})</p>
            </div>
          </div>
          <div class="data flex flex_ac flex_jsb">
            <div class="flex_dc flex_v">
              <span>{{ Number(income.audit_income) >= 10000 ? (Number(income.audit_income) / 10000).toFixed(2) : income.audit_income }}</span>
              <p>审核资料收益({{ Number(income.audit_income) >= 10000 ? '万' : '元' }})</p>
            </div>
            <div class="flex_dc flex_v">
              <span>{{ Number(income.after_income) >= 10000 ? (Number(income.after_income) / 10000).toFixed(2) : income.after_income }}</span>
              <p>帮约收益({{ Number(income.after_income) >= 10000 ? '万' : '元' }})</p>
            </div>
            <div class="flex_dc flex_v">
              <span>{{ Number(income.leader_income) >= 10000 ? (Number(income.leader_income) / 10000).toFixed(2) : income.leader_income }}</span>
              <p>团队收益({{ Number(income.leader_income) >= 10000 ? '万' : '元' }})</p>
            </div>
          </div>
        </template>
      </template>
      <template v-else>
        <div class="data flex flex_ac flex_jsb skelecton">
          <div class="flex_dc flex_v">
            <span>&emsp;&nbsp;</span>
            <p>&emsp;&emsp;&emsp;&emsp;&emsp;</p>
          </div>
          <div class="flex_dc flex_v">
            <span>&emsp;&nbsp;</span>
            <p>&emsp;&emsp;&emsp;&emsp;&emsp;</p>
          </div>
          <div class="flex_dc flex_v">
            <span>&emsp;&nbsp;</span>
            <p>&emsp;&emsp;&emsp;&emsp;&emsp;</p>
          </div>
        </div>
      </template>
      <div class="btn flex_dc">
        <span @click="$router.push('/rwincome')" class="flex_dc">查看明细</span>
        <span @click="goWithdraw" class="flex_dc">立即提现</span>
      </div>
      <div class="list">
        <div class="nav flex flex_ac" :class="is_round ? 'round' : ''" ref="nav_box">
          <span @click="changClassify('rwreg')" :class="view == 'rwreg' ? 'current' : ''">拉新奖励</span>
          <span @click="changClassify('rwbuy')" :class="view == 'rwbuy' ? 'current' : ''">消费分成</span>
          <span v-if="unionInfo.level3_flag == 1" @click="changClassify('rwaudit')" :class="view == 'rwaudit' ? 'current' : ''">审核分成</span>
          <span v-if="unionInfo.level3_flag == 1" @click="changClassify('rwafter')" :class="view == 'rwafter' ? 'current' : ''">帮约分成</span>
          <span v-if="unionInfo.level4_flag == 1" @click="changClassify('rwshop')" :class="view == 'rwshop' ? 'current' : ''">门店分成</span>
        </div>
        <income_rwreg @search="rwregInputName" :name="rwreg_name" v-if="view == 'rwreg'" :list="list" :data="rwreg_data" />
        <income_rwbuy @search="rwbuyInputName" :name="rwbuy_name" v-else-if="view == 'rwbuy'" :list="list" :data="rwbuy_data" />
        <income_rwaudit v-else-if="view == 'rwaudit'" :list="list" :data="rwaudit_data" />
        <income_rwafter v-else-if="view == 'rwafter'" :list="list" :data="rwafter_data" />
        <income_crmfinance v-else-if="view == 'rwshop'" :list="list" :data="rwshop_data" />
      </div>
    </div>
  </oeui-list>
  <tabbars />

  <oe_popup ref="needRz_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，您还未进行实名认证，无法进行提现操作</p>
      <div @click="goIdrz" class="event">去认证</div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needMobile_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，您还未进行手机号认证，无法进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needMobile_dialog.close()
            proxy.$refs.noteMobileRz.open()
          }
        "
        class="event"
      >
        去认证
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needWeixin_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，你还未预留微信相关提现账户信息，完善后方可进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needWeixin_dialog.close()
            router.push('/account')
          }
        "
        class="event"
      >
        去完善
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needAlipay_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，你还未预留支付宝相关提现账户信息，完善后方可进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needAlipay_dialog.close()
            router.push('/account')
          }
        "
        class="event"
      >
        去完善
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needBank_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，你还未预留银行卡相关提现账户信息，完善后方可进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needBank_dialog.close()
            router.push('/account')
          }
        "
        class="event"
      >
        去完善
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="noteMobileRz" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="noteCodeRz">
      <p class="title">手机号认证</p>
      <p class="tips">完成手机认证，确保您的账户安全</p>
      <div class="mobile flex flex_ac">
        <span>手机号：</span>
        <input style="width: 2.4rem" type="number" maxlength="11" v-model="mobile" placeholder="输入手机号" />
      </div>
      <div class="code flex flex_ac flex_jsb">
        <div class="flex flex_ac">
          <span class="flex_s" style="color: #666">验证码：</span>
          <input style="width: 2.4rem" type="number" maxlength="6" v-model="mobilecode" placeholder="短信验证码" />
        </div>
        <span class="getcode flex_s" @click="getCode" v-if="codeStatus">获取验证码</span>
        <span class="time flex_s" v-else>({{ codeSecond }}s后重新获取)</span>
      </div>
      <div @click="sendMobilerz" class="event">确定</div>
      <span @click="proxy.$refs.noteMobileRz.close()" class="close iconfont icon-guanbi"></span>
    </div>
  </oe_popup>

  <oe_hint ref="hint_dialog" />

  <!--审核-->
  <oe_popup ref="audit_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="audit_dialog">
      <p class="title">资料审核</p>
      <p class="tip">请如实审核用户资料，通过用户填写的 信息是否真实，完善度是否达到要求。 该操作不可逆，请谨慎操作。</p>
      <div class="btn flex_dc">
        <span @click="failAuditEvent" class="fail">不通过</span>
        <span @click="sendAuditPass">通过</span>
      </div>
      <span @click="proxy.$refs.audit_dialog.close()" class="close iconfont icon-guanbi2"></span>
    </div>
  </oe_popup>

  <oe_popup ref="auditResult_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">不奖励原因</p>
      <p class="tip">{{ fail_result }}</p>
      <div @click="proxy.$refs.auditResult_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <oe_popup ref="auditFail_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="auditFail_dialog">
      <p class="title">审核不通过原因</p>
      <div class="content">
        <textarea v-model="fail_remark" maxlength="200" placeholder="请输入审核不通过的原因"></textarea>
        <span class="nums">{{ fail_remark.length }}/200</span>
      </div>
      <div class="btn flex_dc">
        <span @click="sendAuditFail(false)" class="fail">不填</span>
        <span @click="sendAuditFail(true)">确定</span>
      </div>
      <span
        @click="
          proxy.$refs.auditFail_dialog.close(() => {
            fail_remark = ''
          })
        "
        class="close iconfont icon-guanbi2"
      ></span>
    </div>
  </oe_popup>

  <!--帮约-->
  <!--开始帮约-->
  <oe_popup ref="after_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="audit_dialog">
      <p class="title">开始帮约</p>
      <p class="tip">请如实匹配双方情况，积极帮约用户完成帮约牵线目标。</p>
      <div class="btn flex_dc">
        <span @click="proxy.$refs.after_dialog.close()" class="fail" style="background: #f2f4f5; color: #666">稍后再说</span>
        <span @click="sendAfterStart">开始帮约</span>
      </div>
    </div>
  </oe_popup>

  <!--帮约失败-->
  <oe_popup ref="afterFail_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">不奖励原因</p>
      <p class="tip">{{ fail_result }}</p>
      <div @click="proxy.$refs.afterFail_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <!--帮约结果-->
  <oe_popup ref="afterResult_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="afterResult_dialog">
      <p class="title">帮约结果</p>
      <div class="content">
        <p class="tip">请如实选择帮约结果且可上传佐证图片，平台会对帮约用户不定时进行抽检，如发现不实牵线，平台有权扣除相应奖励，并收回你的服务权限。</p>
        <div class="type">
          <p>选择帮约结果</p>
          <div class="radio">
            <span @click="changAfterType(2)">
              <i class="iconfont" :class="after_type == 2 ? 'icon-redio_checked' : 'icon-rediobtn_nor'"></i>
              帮约成功
            </span>
            <span @click="changAfterType(3)">
              <i class="iconfont" :class="after_type == 3 ? 'icon-redio_checked' : 'icon-rediobtn_nor'"></i>
              帮约失败
            </span>
          </div>
        </div>
        <div class="proof" v-show="after_type == 2">
          <p>上传佐证（{{ after_list.length }}/9）</p>
          <div class="item flex">
            <span class="flex_s img" @click="deleteImg(item)" v-for="item in after_list" :key="item">
              <img v-lazy="item" alt="" />
              <i class="iconfont icon-guanbi1"></i>
            </span>
            <span @click="proxy.$refs.uploadBox.open(true)" v-show="after_list.length < 9" class="flex_s flex_dc iconfont icon-fabu-01"></span>
          </div>
        </div>
        <div class="remark" v-show="after_type == 3">
          <p>失败原因</p>
          <div class="box">
            <textarea v-model="fail_remark" maxlength="200" placeholder="请输入帮约失败的原因"></textarea>
            <span class="nums">{{ fail_remark.length }}/200</span>
          </div>
        </div>
      </div>
      <div class="btn flex_dc">
        <span
          @click="
            proxy.$refs.afterResult_dialog.close(() => {
              fail_remark = ''
              after_type = 2
            })
          "
          class="fail"
          style="background: #f2f4f5; color: #666"
        >
          取消
        </span>
        <span @click="sendAfterEnd()">确定</span>
      </div>
    </div>
  </oe_popup>
  <upload-headimg ref="uploadBox" @callback="sendImg"></upload-headimg>
  <notRole ref="notRoleBox" />

  <ver-fication ref="oeVerFicat" @callback="smsCallback"></ver-fication>
</template>

<script>
export default {
  name: 'Income',
}
</script>

<script setup>
import { ref, getCurrentInstance, computed, provide, reactive, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import oe_hint from '@/components/hint.vue'
import oe_popup from '@/oeui/popup.vue'
import tabbars from '@/components/tabbars.vue'
import oeuiList from '@/oeui/list.vue'
import income_rwreg from './income_rwreg.vue'
import income_rwbuy from './income_rwbuy.vue'
import income_rwaudit from './income_rwaudit.vue'
import income_rwafter from './income_rwafter.vue'
import income_crmfinance from './income_crmfinance.vue'
import notRole from '@/views/index/components/notRole.vue'
import uploadHeadimg from '@/components/upload_headimg.vue'
import { getRwreg, getRwbuy } from '@/api/income.js'
import { initWithdraw, getWithdrawQrcode, submitMobilerz } from '@/api/withdraw.js'
import { getAuditUser, auditPass, auditFail } from '@/api/audit_user.js'
import { getAfterUser, afterStart, afterEnd } from '@/api/after.js'
import { getCrmfinance } from '@/api/finance.js'
import { uploadImage } from '@/api/edit.js'
import { codeSend } from '@/api/login.js'
import verFication from '@/components/verification.vue'

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const router = useRouter()

const unionInfo = computed(() => store.state.unionInfo)
const income = computed(() => store.state.income)
const view = ref('rwreg')
const is_round = ref(true)

const pageScroll = val => {
  if (val >= proxy.$refs.nav_box.offsetTop) {
    is_round.value = false
  } else {
    is_round.value = true
  }
}

const getPgroupUrl = (type, val) => {
  return require(`@/assets/images/grade/${type}${val}.png`)
}

const changClassify = val => {
  view.value = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const list = ref([])
const page = ref(1)
const pageCount = ref('0')
const listStatus = ref('loading')

const rwreg_name = ref('')
const rwbuy_name = ref('')

const rwreg_data = reactive({
  rw_amount: 0,
  total0: 0,
  total1: 0,
})
const rwbuy_data = reactive({
  user_total: 0,
  income_amount: 0,
})

const rwaudit_data = reactive({
  audit_income: 0,
  total: 0,
  total0: 0,
  total1: 0,
})
const rwafter_data = reactive({
  after_income: 0,
  total: 0,
  total0: 0,
  total1: 0,
  total2: 0,
  total3: 0,
})

const rwshop_data = reactive({
  total_orders: 0,
  crmfinance_amount: 0,
  crmorder_income: 0,
})

const rwregInputName = val => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  rwreg_name.value = val
  getList(true)
}
const rwbuyInputName = val => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  rwbuy_name.value = val
  getList(true)
}

const initList = () => {
  if (view.value == 'rwreg') {
    return getRwreg({
      page: page.value,
      s_name: rwreg_name.value,
      s_rwflag: 1,
    })
  } else if (view.value == 'rwbuy') {
    return getRwbuy({
      s_name: rwbuy_name.value,
      page: page.value,
    })
  } else if (view.value == 'rwaudit') {
    return getAuditUser({
      page: page.value,
      s_settle: 1,
    })
  } else if (view.value == 'rwafter') {
    return getAfterUser({
      page: page.value,
      s_settle: 2,
    })
  } else if (view.value == 'rwshop') {
    return getCrmfinance({
      s_settle: 1,
      page: page.value,
    })
  }
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  initList().then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      //更新列表统计数据
      initListTj(res.result)
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const initListTj = data => {
  if (view.value == 'rwreg' && page.value == 1) {
    rwreg_data.rw_amount = data.rw_amount
    rwreg_data.total0 = data.total0
    rwreg_data.total1 = data.total1
  } else if (view.value == 'rwbuy' && page.value == 1) {
    rwbuy_data.user_total = data.user_total
    rwbuy_data.income_amount = data.income_amount
  } else if (view.value == 'rwaudit' && page.value == 1) {
    rwaudit_data.audit_income = data.audit_income
    rwaudit_data.total = data.total
    rwaudit_data.total0 = data.total0
    rwaudit_data.total1 = data.total1
  } else if (view.value == 'rwafter' && page.value == 1) {
    rwafter_data.after_income = data.after_income
    rwafter_data.total = data.total
    rwafter_data.total0 = data.total0
    rwafter_data.total1 = data.total1
    rwafter_data.total2 = data.total2
    rwafter_data.total3 = data.total3
  } else if (view.value == 'rwshop' && page.value == 1) {
    rwshop_data.total_orders = data.total_orders
    rwshop_data.crmfinance_amount = data.crmfinance_amount
    rwshop_data.crmorder_income = data.crmorder_income
  }
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
  store.dispatch('getIncome')
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

//去提现
const idrz_type = ref('')
const goWithdraw = () => {
  initWithdraw().then(res => {
    if (res.ret == 1) {
      if (res.result.need_rz == 1) {
        // 未实名认证
        idrz_type.value = res.result.idrz_type
        proxy.$refs.needRz_dialog.open()
      } else if (res.result.need_wx == 1) {
        // 未关注公众号
        getWithdrawQrcode().then(res => {
          if (res.ret == 1) {
            proxy.$refs.hint_dialog.open({
              title: '提示',
              tips: '尊敬的用户，为方便后续提现操作，请先关注公众号',
              tap: '长按识别二维码关注公众号',
              qrcode: res.result.data,
            })
          } else {
            OEUI.toast({
              text: res.msg || '系统繁忙，请稍后再试',
            })
          }
        })
      } else if (res.result.need_mobilerz == 1) {
        // 需手机认证
        mobile.value = unionInfo.value.mobile
        proxy.$refs.needMobile_dialog.open()
      } else if (res.result.need_wxinfo == 1) {
        // 需完善微信账号
        proxy.$refs.needWeixin_dialog.open()
      } else if (res.result.need_alipay == 1) {
        // 需完善支付宝账号
        proxy.$refs.needAlipay_dialog.open()
      } else if (res.result.need_bank == 1) {
        // 完善银行卡账号
        proxy.$refs.needBank_dialog.open()
      } else {
        router.push('/withdraw')
      }
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

//去实名
const goIdrz = () => {
  proxy.$refs.needRz_dialog.close()
  if (idrz_type.value == 1) {
    // 三要素
    router.push('/idrz')
  } else {
    // 人工核验
    router.push('/idrz2')
  }
}

const mobilecode = ref(null)
const codeSecond = ref(60)
const codeStatus = ref(true)

const mobile = ref('')

let time = null
const getCode = () => {
  //判断是否开启了防刷机制
  if (mobile.value == '') {
    OEUI.toast({
      text: '请填写手机号',
    })
    return
  }
  proxy.$refs.oeVerFicat.start()
}

const sendCode = code => {
  //发送验证码
  OEUI.loading.show()

  codeSend({
    type: 'checkcode',
    mobile: mobile.value,
    brushcode: code || '',
  }).then(res => {
    OEUI.loading.hide()
    if (res.ret == 1) {
      OEUI.toast({ text: '发送验证码成功' })
      codeStatus.value = false
      if (code) {
        proxy.$refs.oeVerFicat.success()
      }
      if (time != null) return
      time = setInterval(() => {
        codeSecond.value--
        if (codeSecond.value == 0) {
          clearInterval(time)
          time = null
          codeStatus.value = true
          codeSecond.value = 60
        }
      }, 1000)
    } else {
      if (code) {
        proxy.$refs.oeVerFicat.error()
      }
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const smsCallback = code => {
  //防刷回调
  sendCode(code)
}

const sendMobilerz = () => {
  if (!mobilecode.value)
    return OEUI.toast({
      text: '请输入短信验证码',
    })
  codeSecond.value = 60
  codeStatus.value = true
  OEUI.loading.show()
  submitMobilerz({
    mobile: mobile.value,
    mobilecode: mobilecode.value,
  })
    .then(res => {
      mobilecode.value = null
      OEUI.loading.hide()
      OEUI.toast({ text: '手机号认证成功' })
      store.commit('setUnionInfo', res.result.info)
      proxy.$refs.noteMobileRz.close()
    })
    .catch(() => {
      OEUI.loading.hide()
      OEUI.toast('系统繁忙，请稍后再试')
    })
}

//审核操作
const auditid = ref(null)
const fail_remark = ref('')
const is_send = ref(true)
const fail_result = ref('')

const selectAudit = id => {
  auditid.value = id
  proxy.$refs.audit_dialog.open()
}
provide('selectAudit', selectAudit)
const failAuditEvent = () => {
  proxy.$refs.audit_dialog.close(() => {
    proxy.$refs.auditFail_dialog.open()
  })
}
const sendAuditPass = () => {
  if (!is_send.value) return
  is_send.value = false
  auditPass({
    id: auditid.value,
  }).then(res => {
    if (res.ret == 1) {
      list.value.map(v => {
        if (v.userid == auditid.value) {
          v.flag = 1
        }
      })
      proxy.$refs.audit_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        auditid.value = null
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const sendAuditFail = flag => {
  if (flag && !fail_remark.value.length) {
    return OEUI.toast({
      text: '请填写审核不通过的原因',
    })
  }
  if (!is_send.value) return
  is_send.value = false
  auditFail({
    id: auditid.value,
    remark: fail_remark.value,
  }).then(res => {
    if (res.ret == 1) {
      list.value.map(v => {
        if (v.userid == auditid.value) {
          v.flag = 2
        }
      })
      proxy.$refs.auditFail_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        auditid.value = null
        fail_remark.value = ''
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const showFailAudit = val => {
  fail_result.value = val
  proxy.$refs.afterFail_dialog.open()
}
provide('showFailAudit', showFailAudit)

//帮约操作
const changAfterFlag = type => {
  after_flag.value = type
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
//帮约操作

const afterid = ref(null)
const after_type = ref(2)
const after_list = ref([])
const changAfterType = val => {
  after_type.value = val
}
const sendImg = obj => {
  if (!obj.src) return OEUI.toast({ text: '图片获取失败，请检查!' })
  try {
    uploadImage({
      base64img: obj.src,
      module: 'upload',
      thumb: 1,
    }).then(data => {
      if (data.ret == 1) {
        after_list.value.push(data.result.drawimg_url)
      } else {
        OEUI.toast({ text: '图片上传失败，请检查!' })
      }
    })
  } catch (error) {
    OEUI.toast({ text: '系统繁忙，请检查!' })
  }
}
const deleteImg = val => {
  after_list.value = after_list.value.filter(item => item != val)
}
const ininAfterList = () => {
  if (!after_list.value.length) return
  let data = {}
  after_list.value.forEach((v, i) => {
    data['link_img' + (i + 1)] = v
  })
  return data
}

const afterStartEvent = id => {
  afterid.value = id
  proxy.$refs.after_dialog.open()
}
provide('afterStart', afterStartEvent)
const AfterEvent = id => {
  afterid.value = id
  proxy.$refs.afterResult_dialog.open()
}

provide('selectAfter', AfterEvent)

const sendAfterStart = () => {
  if (!is_send.value) return
  is_send.value = false
  afterStart({
    id: afterid.value,
  }).then(res => {
    if (res.ret == 1) {
      list.value.map(v => {
        if (v.afterid == afterid.value) {
          v.flag = 1
        }
      })
      proxy.$refs.after_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        afterid.value = null
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const sendAfterEnd = () => {
  if (!is_send.value) return
  is_send.value = false
  afterEnd({
    id: afterid.value,
    flag: after_type.value,
    remark: fail_remark.value,
    ...ininAfterList(),
  }).then(res => {
    if (res.ret == 1) {
      list.value.map(v => {
        if (v.afterid == afterid.value) {
          v.flag = after_type.value
        }
      })
      proxy.$refs.afterResult_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        after_type.value = 2
        afterid.value = null
        fail_remark.value = ''
        after_list.value = []
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const showFailAfter = val => {
  fail_result.value = val
  proxy.$refs.afterFail_dialog.open()
}
provide('showFailAfter', showFailAfter)

getList(true)

watch(
  () => unionInfo.value,
  newInfo => {
    if (newInfo) {
      if (newInfo.level1_flag != 1) {
        nextTick(() => {
          proxy.$refs.notRoleBox.open(newInfo.level1_flag)
        })
      }
      unionInfo.value = newInfo
    }
  },
  { immediate: true, deep: true },
)
watch(
  () => income.value,
  newData => {
    if (newData) {
      income.value = newData
    }
  },
  { immediate: true, deep: true },
)
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}

.font_din {
  font-family: 'cpDin';
}

.main {
  .user {
    padding: 0.48rem 0.4267rem 0 0.4267rem;

    .head {
      width: 1.0133rem;
      height: 1.0133rem;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 0.2133rem;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .name {
      max-width: 40vw;
      color: #31293b;
      font-size: 0.4267rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      line-height: 0.5867rem;
      margin-right: 0.1067rem;
    }

    .name_box {
      em {
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #666666;
        line-height: 1;
        position: relative;
        top: 0.1333rem;
      }
    }

    .grade {
      margin-top: 0.08rem;
      padding-left: 0.16rem;
      display: flex;
      align-items: center;
      overflow-x: scroll;

      &::-webkit-scrollbar {
        display: none;
      }

      .group {
        flex-shrink: 0;
      }
    }
  }

  .user_skelecton {
    padding: 0.48rem 0.4267rem 0 0.4267rem;

    .head {
      width: 1.0133rem;
      height: 1.0133rem;
      border-radius: 50%;
      margin-right: 0.2133rem;
      background: #f1f1f1;
    }

    p {
      height: 0.48rem;
      border-radius: 0.1333rem;
      background: #f1f1f1;
      width: 40vw;
    }

    span {
      margin-top: 0.16rem;
      height: 0.32rem;
      width: 1.3333rem;
      margin-right: 0.16rem;
      background: #f1f1f1;
      border-radius: 0.08rem;
    }
  }

  & > .data {
    margin-top: 0.8rem;
    padding: 0 0.4267rem;

    & > div {
      flex: 1;

      span {
        font-size: 0.6933rem;
        line-height: 0.7733rem;
        font-weight: 700;
        color: #31293b;
        @extend .font_din;
      }

      p {
        font-size: 0.32rem;
        line-height: 0.4533rem;
        color: #999;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        margin-top: 0.1067rem;
      }
    }

    &.skelecton {
      span {
        background: #f1f1f1;
        border-radius: 0.1333rem;
      }

      p {
        background: #f1f1f1;
        border-radius: 0.08rem;
      }
    }
  }

  & > .btn {
    padding: 0.7467rem 0.4267rem 0.64rem 0.4267rem;

    & > span:first-child {
      margin-right: 0.9067rem;
      color: #7d68fe;
      border: 0.0267rem solid #7d68fe;
      background: transparent;
    }

    & > span {
      cursor: pointer;
      flex: 1;
      flex-shrink: 0;
      background: #7d68fe;
      color: #fff;
      border: 0.0267rem solid #7d68fe;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
      padding: 0.1333rem 0;
      border-radius: 0.4267rem;
    }
  }

  .list {
    .nav {
      padding: 0 0.4267rem;
      background: #fff;
      height: 1.5733rem;
      position: sticky;
      top: -0.0267rem;
      z-index: 200;
      border-bottom: 0.0267rem solid #f4f6f7;

      &.round {
        border-radius: 0.4267rem 0.4267rem 0 0;
      }

      overflow-x: scroll;

      &::-webkit-scrollbar {
        display: none;
      }

      span {
        flex-shrink: 0;
        cursor: pointer;
        margin-right: 1.0667rem;
        font-size: 0.3733rem;
        line-height: 0.6133rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        position: relative;
        top: 0.0533rem;

        &.current {
          font-size: 0.48rem;
          line-height: 0.6667rem;
          font-weight: 500;
          color: #31293b;
          top: 0;
          position: relative;

          &::after {
            position: absolute;
            content: '';
            width: 0.32rem;
            height: 0.08rem;
            border-radius: 0.0533rem;
            background: #7d68fe;
            left: 50%;
            transform: translateX(-50%);
            bottom: -0.0533rem;
          }
        }
      }
    }
  }
}

.audit_dialog {
  padding: 0.64rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;

  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }

  .tip {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.5867rem;
  }

  .btn {
    margin-top: 0.5333rem;

    span {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }

    .fail {
      margin-right: 0.5867rem;
      color: $color_main;
      background: rgba(125, 104, 254, 0.24);
    }
  }

  .close {
    position: absolute;
    right: 0.4rem;
    top: 0.2667rem;
    font-size: 0.48rem;
  }
}

.auditFail_dialog {
  padding: 0.4267rem 0.64rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;

  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }

  .content {
    margin-top: 0.32rem;
    background: #f2f4f5;
    height: 2.8rem;
    border-radius: 0.32rem;
    padding: 0.32rem;
    box-sizing: border-box;
    position: relative;

    textarea {
      resize: none;
      border: none;
      width: 100%;
      height: 100%;
      background: none;

      &::placeholder {
        font-size: 0.3733rem;
        font-weight: normal;
        color: #c5c3c7;
        line-height: 0.5333rem;
      }
    }

    .nums {
      position: absolute;
      font-size: 0.32rem;
      font-weight: normal;
      color: #c5c3c7;
      line-height: 0.4533rem;
      right: 0.4rem;
      bottom: 0.0533rem;
    }
  }

  .btn {
    margin-top: 0.4rem;
    padding: 0 0.2133rem;

    span {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }

    .fail {
      margin-right: 0.5867rem;
      color: $color_main;
      background: rgba(125, 104, 254, 0.24);
    }
  }

  .close {
    position: absolute;
    right: 0.4rem;
    top: 0.2667rem;
    font-size: 0.48rem;
  }
}

.afterResult_dialog {
  height: 60vh;
  padding: 0.64rem 0;
  font-family: PingFang SC, PingFang SC;

  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }

  .content {
    padding: 0 0.64rem;
    max-height: 80%;
    min-height: 80%;
    overflow-y: scroll;

    .tip {
      margin-top: 0.4267rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5867rem;
    }

    .type {
      margin-top: 0.4267rem;

      p {
        font-size: 0.3733rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5333rem;
      }

      .radio {
        margin-top: 0.2133rem;

        span {
          margin-right: 0.8533rem;
          font-size: 0.3733rem;
          font-weight: normal;
          color: #666666;
          line-height: 0.5333rem;

          i {
            position: relative;
            top: 0.0533rem;
            color: #999999;
            font-size: 0.48rem;
          }

          .icon-redio_checked {
            color: $color_main;
          }
        }
      }
    }

    .proof {
      margin-top: 0.4267rem;

      p {
        font-size: 0.3733rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5333rem;
      }

      .item {
        margin-top: 0.2133rem;
        flex-wrap: wrap;

        span {
          width: 1.8667rem;
          height: 1.8667rem;
          background: #f2f4f5;
          margin-right: 0.5067rem;
          margin-bottom: 0.2667rem;
          border-radius: 0.3733rem;
          overflow: hidden;
          font-size: 0.96rem;
          color: #999;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .icon-guanbi1 {
            position: absolute;
            right: 0.0267rem;
            top: 0.0267rem;
            color: #ed1616;
            z-index: 100;
          }
        }

        & > span:nth-child(3n) {
          margin-right: 0;
        }
      }
    }

    .remark {
      margin-top: 0.4267rem;

      p {
        font-size: 0.3733rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5333rem;
      }

      .box {
        margin-top: 0.2133rem;
        background: #f2f4f5;
        height: 2.8rem;
        border-radius: 0.32rem;
        padding: 0.32rem;
        box-sizing: border-box;
        position: relative;

        textarea {
          resize: none;
          border: none;
          width: 100%;
          height: 100%;
          background: none;

          &::placeholder {
            font-size: 0.3733rem;
            font-weight: normal;
            color: #c5c3c7;
            line-height: 0.5333rem;
          }
        }

        .nums {
          position: absolute;
          font-size: 0.32rem;
          font-weight: normal;
          color: #c5c3c7;
          line-height: 0.4533rem;
          right: 0.4rem;
          bottom: 0.0533rem;
        }
      }
    }
  }

  .btn {
    margin-top: 0.4rem;
    padding: 0 0.64rem;

    span {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }

    .fail {
      margin-right: 0.5867rem;
      color: $color_main;
      background: rgba(125, 104, 254, 0.24);
    }
  }
}

.fail_dialog {
  position: relative;

  .close {
    top: 0.2667rem;
    right: 0.4rem;
    font-size: 0.48rem;
  }
}

.noteCodeRz {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.4267rem;

  .title {
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
  }

  .iconfont {
    margin: 0 auto;
  }

  .tips {
    text-align: center;
    margin-top: 0.5333rem;
    font-size: 0.3733rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #666666;
  }

  .price {
    text-align: center;
    margin-top: 0.1067rem;
    font-size: 0.64rem;
    line-height: 0.9067rem;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #31293b;
  }

  .mobile {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #666666;
    padding: 0.32rem 0;
  }

  .code {
    padding: 0.32rem 0;
    margin-bottom: 0.5333rem;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;

    .getcode {
      color: #0570f1;
    }

    .time {
      color: #999;
    }
  }

  .event {
    text-align: center;
    background: $color_main;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin: 0 0.7733rem;
    padding: 0.24rem 0;
  }

  .close {
    position: absolute;
    font-size: 0.7467rem;
    color: #fff;
    left: 50%;
    transform: translateX(-50%);
    bottom: -1.3333rem;
  }
}
</style>
