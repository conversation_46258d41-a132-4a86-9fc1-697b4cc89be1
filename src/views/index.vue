<!-- @format -->

<template>
  <div class="oe_index">
    <oe-navs title="" @navMore="navMore"> </oe-navs>
    <top-bg></top-bg>
    <div class="loda_box">
      <oe-load></oe-load>
    </div>
  </div>
</template>

<script setup>
  import oeNavs from '@/components/navs.vue'
  import topBg from '@/components/top_bg.vue'
  import oeLoad from '@/components/load.vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import { watch, nextTick } from 'vue'
  const store = useStore()
  const router = useRouter()

  const routerToPath = info => {
    if (info.unionid) {
        router.replace('/home')
    } else {
        router.replace('/reg')
    }
  }

  watch(
    () => store.state.unionInfo,
    newValue => {
      nextTick(() => {
        routerToPath(newValue)
      })
    },
    { deep: true, immediate: true }
  )
</script>
<style lang="scss" scoped>
  .oe_index {
    position: relative;
    .loda_box {
      padding-top: 100px;
      text-align: center;
      .loading {
        margin: 0 auto;
      }
    }
  }
</style>
