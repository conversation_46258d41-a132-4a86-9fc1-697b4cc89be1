<!-- @format -->

<template>
  <div class="top_box">
    <img src="@/assets/images/bg_main.png" />
  </div>
  <div class="pr" style="z-index: 1; height: 100%">
    <router-view v-slot="{ Component }">
      <!-- <transition :name="$route.meta.transitionName"> -->
      <keep-alive>
        <component v-if="$route.meta.keepAlive" :is="Component" :key="$route.name" />
      </keep-alive>
      <component :is="Component" :key="$route.name" v-if="!$route.meta.keepAlive" />
      <!-- </transition> -->
    </router-view>
  </div>
</template>
<script setup>
  import { getCurrentInstance } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter, useRoute } from 'vue-router'
  const { proxy } = getCurrentInstance()
  const http = proxy.http
  const store = useStore()
  const route = useRoute()
  const router = useRouter()

  const handleFontSize = () => {
    // 设置网页字体为默认大小
    WeixinJSBridge.invoke('setFontSizeCallback', { fontSize: 0 })
    // 重写设置网页字体大小的事件
    WeixinJSBridge.on('menu:setfont', function () {
      WeixinJSBridge.invoke('setFontSizeCallback', { fontSize: 0 })
    })
  }
  // 安卓 禁止用户自定义设置字体大小
  try {
    if (typeof WeixinJSBridge == 'object' && typeof WeixinJSBridge.invoke == 'function') {
      handleFontSize()
    } else {
      if (document.addEventListener) {
        document.addEventListener('WeixinJSBridgeReady', handleFontSize, false)
      } else if (document.attachEvent) {
        document.attachEvent('WeixinJSBridgeReady', handleFontSize)
        document.attachEvent('onWeixinJSBridgeReady', handleFontSize)
      }
    }
  } catch (error) {
    console.log('错误')
  }

  try {
    //禁止双指放大
    document.documentElement.addEventListener(
      'touchstart',
      function (event) {
        if (event.touches.length > 1) {
          event.preventDefault()
        }
      },
      false
    )
    //禁止双击放大
    var lastTouchEnd = 0
    document.documentElement.addEventListener(
      'touchend',
      function (event) {
        var now = Date.now()
        if (now - lastTouchEnd <= 300) {
          event.preventDefault()
        }
        lastTouchEnd = now
      },
      false
    )
  } catch (error) {
    console.log('错误')
  }

  const isWeiXin = () => {
    var ua = window.navigator.userAgent.toLowerCase()
    //通过正则表达式匹配ua中是否含有MicroMessenger字符串
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
      return true
    } else {
      return false
    }
  }

  let config = {}
  const init = isGetHC => {
    store.dispatch('getConfig', isGetHC).then(res => {
      if (res && res.ret == 1) {
        config = res.result
        getUserInfo()
        //document.getElementsByTagName('body')[0].style.setProperty('--colorMain', config.wap_layout_main_color || '#7D68FF')
        document.title = config.union_modname
        var link = document.querySelector("link[rel*='icon']")
        if (link) link.href = config.siteurl + 'favicon.ico'
      }
    })
    if (isWeiXin()) {
      store.dispatch('getWechatUp')
    }
  }

  const getUserInfo = () => {
    //选择获取登录资料方式
    store.dispatch('getUserInfo').then(res => {
      if (res && res.ret == -10) {
        //cookie 与 loginsign 不匹配
        store.dispatch('getCookieUser').then(result => {
          //登录失败
          if (result && result.ret != 1) {
            router.replace('/reg')
          }
        })
      }
    })
  }

  const editConfigTime = () => {
    http
      .post('', {
        m: 'vuewap',
        c: 'picker',
        a: 'cfgtime'
      })
      .then(res => {
        if (res.ret == 1) {
          let isCtime = localStorage.getItem('newConfigTime') || ''
          init(isCtime == res.result)
          localStorage.setItem('newConfigTime', res.result)
        } else {
          init()
        }
      })
      .catch(() => {
        init()
      })
  }
  //init()
  editConfigTime()

  window.onload = function () {
    document.addEventListener('gesturestart', function (e) {
      e.preventDefault()
    })
    document.addEventListener('dblclick', function (e) {
      e.preventDefault()
    })
    document.addEventListener('touchstart', function (event) {
      if (event.touches.length > 1) {
        event.preventDefault()
      }
    })
    var lastTouchEnd = 0
    document.addEventListener(
      'touchend',
      function (event) {
        var now = new Date().getTime()
        if (now - lastTouchEnd <= 300) {
          event.preventDefault()
        }
        lastTouchEnd = now
      },
      false
    )
  }

  store.dispatch('getLoginStatus')
  store.dispatch('getTj')
  store.dispatch('getIncome')

</script>

<style lang="scss">
  @import '~@/style/default.scss';
  @import '~@/style/comm.scss';
  @import url('~@/assets/iconfont/iconfont.css');
  @import url('~@/oeui/icon/iconfont.css');

  #app {
    position: relative;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    touch-action: manipulation;
    /* 23.11.8 ios禁止字体转换 */
    -webkit-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
    -moz-text-size-adjust: 100% !important;
  }

  #app::-webkit-scrollbar {
    display: none;
  }
</style>
